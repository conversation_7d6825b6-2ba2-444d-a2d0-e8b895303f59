import fs from "fs";
import path from "path";
import { getCodebaseIndexingLogsPath, getCodebaseRetrievalLogsPath } from "../util/paths.js";

export type LogLevel = "debug" | "info" | "warn" | "error";

export interface BaseLogEntry {
  timestamp: string;
  level: LogLevel;
  sessionId: string;
  workspaceDir?: string;
}

// 索引流程相关的日志条目类型
export interface IndexingStartLogEntry extends BaseLogEntry {
  type: "indexing_start";
  data: {
    totalFiles: number;
    indexType: string;
    branch?: string;
  };
}

export interface FileProcessingLogEntry extends BaseLogEntry {
  type: "file_processing";
  data: {
    filepath: string;
    fileSize: number;
    language?: string;
    processingTimeMs: number;
    chunksGenerated?: number;
    error?: string;
  };
}

export interface ChunkExtractionLogEntry extends BaseLogEntry {
  type: "chunk_extraction";
  data: {
    filepath: string;
    totalChunks: number;
    chunks: Array<{
      index: number;
      startLine: number;
      endLine: number;
      contentLength: number;
      digest: string;
    }>;
  };
}

export interface VectorizationLogEntry extends BaseLogEntry {
  type: "vectorization";
  data: {
    chunkId: string;
    filepath: string;
    chunkIndex: number;
    embeddingModel: string;
    processingTimeMs: number;
    vectorDimensions?: number;
    error?: string;
  };
}

export interface IndexingCompleteLogEntry extends BaseLogEntry {
  type: "indexing_complete";
  data: {
    totalFiles: number;
    totalChunks: number;
    totalTimeMs: number;
    indexType: string;
    success: boolean;
    errors?: string[];
  };
}

// 检索流程相关的日志条目类型
export interface RetrievalStartLogEntry extends BaseLogEntry {
  type: "retrieval_start";
  data: {
    originalQuery: string;
    fullInput: string;
    nRetrieve: number;
    nFinal: number;
    useReranking: boolean;
    filterDirectory?: string;
  };
}

export interface QueryPreprocessingLogEntry extends BaseLogEntry {
  type: "query_preprocessing";
  data: {
    originalQuery: string;
    processedQuery: string;
    preprocessingSteps: string[];
    processingTimeMs: number;
  };
}

export interface VectorSearchLogEntry extends BaseLogEntry {
  type: "vector_search";
  data: {
    query: string;
    embeddingModel: string;
    searchTimeMs: number;
    resultsCount: number;
    results: Array<{
      filepath: string;
      chunkIndex: number;
      score: number;
      startLine: number;
      endLine: number;
      contentPreview: string; // 前100个字符
    }>;
  };
}

export interface FullTextSearchLogEntry extends BaseLogEntry {
  type: "fulltext_search";
  data: {
    query: string;
    searchTimeMs: number;
    resultsCount: number;
    bm25Threshold: number;
    results: Array<{
      filepath: string;
      chunkIndex: number;
      rank: number;
      startLine: number;
      endLine: number;
      contentPreview: string;
    }>;
  };
}

export interface RerankingLogEntry extends BaseLogEntry {
  type: "reranking";
  data: {
    query: string;
    rerankModel: string;
    inputChunksCount: number;
    outputChunksCount: number;
    processingTimeMs: number;
    scores: number[];
    rerankedResults: Array<{
      filepath: string;
      chunkIndex: number;
      originalScore: number;
      rerankScore: number;
      startLine: number;
      endLine: number;
    }>;
    error?: string;
  };
}

export interface ContextGenerationLogEntry extends BaseLogEntry {
  type: "context_generation";
  data: {
    finalChunksCount: number;
    totalTokens: number;
    contextItems: Array<{
      filepath: string;
      startLine: number;
      endLine: number;
      tokenCount: number;
      contentPreview: string;
    }>;
  };
}

export interface RetrievalCompleteLogEntry extends BaseLogEntry {
  type: "retrieval_complete";
  data: {
    originalQuery: string;
    totalTimeMs: number;
    finalResultsCount: number;
    success: boolean;
    error?: string;
  };
}

export type CodebaseLogEntry = 
  | IndexingStartLogEntry
  | FileProcessingLogEntry
  | ChunkExtractionLogEntry
  | VectorizationLogEntry
  | IndexingCompleteLogEntry
  | RetrievalStartLogEntry
  | QueryPreprocessingLogEntry
  | VectorSearchLogEntry
  | FullTextSearchLogEntry
  | RerankingLogEntry
  | ContextGenerationLogEntry
  | RetrievalCompleteLogEntry;

export interface CodebaseLoggerConfig {
  enabled: boolean;
  logLevel: LogLevel;
  maxLogFileSize: number; // in bytes
  logRetentionDays: number;
  bufferSize: number; // number of entries to buffer before writing
  format?: 'json' | 'human'; // 新增：日志格式选项，默认为json
}

const DEFAULT_CONFIG: CodebaseLoggerConfig = {
  enabled: false,
  logLevel: "info",
  maxLogFileSize: 10 * 1024 * 1024, // 10MB
  logRetentionDays: 7,
  bufferSize: 100,
  format: 'human', // 默认使用人类可读格式
};

export class CodebaseLogger {
  private static instance: CodebaseLogger | null = null;
  private config: CodebaseLoggerConfig;
  private indexingBuffer: CodebaseLogEntry[] = [];
  private retrievalBuffer: CodebaseLogEntry[] = [];
  private sessionId: string;
  private flushTimeout: NodeJS.Timeout | null = null;

  private constructor(config: Partial<CodebaseLoggerConfig> = {}) {
    console.log('[CodebaseLogger] 构造函数被调用，传入配置:', JSON.stringify(config, null, 2));
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.sessionId = this.generateSessionId();
    console.log('[CodebaseLogger] 初始化完成，最终配置:', JSON.stringify(this.config, null, 2));
    console.log('[CodebaseLogger] 生成的会话ID:', this.sessionId);
  }

  public static getInstance(config?: Partial<CodebaseLoggerConfig>): CodebaseLogger {
    console.log('[CodebaseLogger] getInstance被调用，传入配置:', JSON.stringify(config, null, 2));
    if (CodebaseLogger.instance === null) {
      console.log('[CodebaseLogger] 创建新的实例');
      CodebaseLogger.instance = new CodebaseLogger(config);
    } else {
      console.log('[CodebaseLogger] 返回现有实例');
    }
    return CodebaseLogger.instance;
  }

  public updateConfig(config: Partial<CodebaseLoggerConfig>): void {
    console.log('[CodebaseLogger] updateConfig被调用，传入配置:', JSON.stringify(config, null, 2));
    console.log('[CodebaseLogger] 更新前的配置:', JSON.stringify(this.config, null, 2));
    this.config = { ...this.config, ...config };
    console.log('[CodebaseLogger] 更新后的配置:', JSON.stringify(this.config, null, 2));
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: LogLevel): boolean {
    console.log(`[CodebaseLogger] shouldLog检查 - 级别: ${level}, 配置启用状态: ${this.config.enabled}, 配置日志级别: ${this.config.logLevel}`);
    if (!this.config.enabled) {
      console.log('[CodebaseLogger] 日志记录被禁用，跳过记录');
      return false;
    }

    const levels: LogLevel[] = ["debug", "info", "warn", "error"];
    const currentLevelIndex = levels.indexOf(this.config.logLevel);
    const entryLevelIndex = levels.indexOf(level);

    const shouldLog = entryLevelIndex >= currentLevelIndex;
    console.log(`[CodebaseLogger] 日志级别检查结果: ${shouldLog} (当前级别索引: ${currentLevelIndex}, 条目级别索引: ${entryLevelIndex})`);
    return shouldLog;
  }

  private formatLogEntry(entry: CodebaseLogEntry): string {
    if (this.config.format === 'json') {
      return JSON.stringify(entry);
    }

    // Human-readable format
    const timestamp = new Date(entry.timestamp).toLocaleString();
    const level = entry.level.toUpperCase().padEnd(5);
    const type = entry.type.replace(/_/g, ' ').toUpperCase();

    let message = `[${timestamp}] ${level} [${type}]`;

    if (entry.workspaceDir) {
      message += ` [${entry.workspaceDir}]`;
    }

    // Format data based on entry type
    switch (entry.type) {
      case 'indexing_start':
        const startData = entry.data as IndexingStartLogEntry['data'];
        message += ` 开始索引 - 文件数: ${startData.totalFiles}, 类型: ${startData.indexType}`;
        if (startData.branch) message += `, 分支: ${startData.branch}`;
        break;

      case 'file_processing':
        const fileData = entry.data as FileProcessingLogEntry['data'];
        message += ` 处理文件: ${fileData.filepath} (${fileData.fileSize} bytes, ${fileData.processingTimeMs}ms)`;
        if (fileData.chunksGenerated) message += ` -> ${fileData.chunksGenerated} chunks`;
        if (fileData.error) message += ` ERROR: ${fileData.error}`;
        break;

      case 'indexing_complete':
        const completeData = entry.data as IndexingCompleteLogEntry['data'];
        message += ` 索引完成 - 文件: ${completeData.totalFiles}, 块: ${completeData.totalChunks}, 耗时: ${completeData.totalTimeMs}ms`;
        message += ` 状态: ${completeData.success ? '成功' : '失败'}`;
        break;

      case 'retrieval_start':
        const retrievalData = entry.data as RetrievalStartLogEntry['data'];
        message += ` 开始检索: "${retrievalData.originalQuery}" (检索${retrievalData.nRetrieve}个，返回${retrievalData.nFinal}个)`;
        break;

      case 'vector_search':
        const vectorData = entry.data as VectorSearchLogEntry['data'];
        message += ` 向量搜索: "${vectorData.query}" -> ${vectorData.resultsCount}个结果 (${vectorData.searchTimeMs}ms)`;
        break;

      case 'fulltext_search':
        const fulltextData = entry.data as FullTextSearchLogEntry['data'];
        message += ` 全文搜索: "${fulltextData.query}" -> ${fulltextData.resultsCount}个结果 (${fulltextData.searchTimeMs}ms)`;
        break;

      default:
        // For other types, just show the data as JSON
        message += ` ${JSON.stringify(entry.data)}`;
    }

    return message;
  }

  private async writeToFile(entries: CodebaseLogEntry[], isIndexing: boolean): Promise<void> {
    console.log(`[CodebaseLogger] writeToFile被调用 - 条目数量: ${entries.length}, 是否为索引日志: ${isIndexing}`);
    if (entries.length === 0) {
      console.log('[CodebaseLogger] 没有条目需要写入，跳过');
      return;
    }

    const logPath = isIndexing ? getCodebaseIndexingLogsPath() : getCodebaseRetrievalLogsPath();
    console.log(`[CodebaseLogger] 目标日志文件路径: ${logPath}`);

    try {
      // Check file size and rotate if necessary
      console.log('[CodebaseLogger] 检查文件大小并在必要时轮转');
      await this.rotateLogFileIfNeeded(logPath);

      const logLines = entries.map(entry => this.formatLogEntry(entry)).join('\n') + '\n';
      console.log(`[CodebaseLogger] 准备写入 ${logLines.length} 字符到日志文件 (格式: ${this.config.format})`);
      await fs.promises.appendFile(logPath, logLines, 'utf8');
      console.log('[CodebaseLogger] 成功写入日志文件');
    } catch (error) {
      console.error(`[CodebaseLogger] 写入codebase日志失败: ${error}`);
      console.error(`[CodebaseLogger] 错误详情:`, error);
    }
  }

  private async rotateLogFileIfNeeded(logPath: string): Promise<void> {
    try {
      const stats = await fs.promises.stat(logPath);
      if (stats.size > this.config.maxLogFileSize) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedPath = `${logPath}.${timestamp}`;
        await fs.promises.rename(logPath, rotatedPath);
      }
    } catch (error) {
      // File doesn't exist yet, which is fine
    }
  }

  private scheduleFlush(): void {
    console.log('[CodebaseLogger] scheduleFlush被调用');
    if (this.flushTimeout) {
      console.log('[CodebaseLogger] 清除现有的刷新定时器');
      clearTimeout(this.flushTimeout);
    }

    console.log('[CodebaseLogger] 设置5秒后刷新定时器');
    this.flushTimeout = setTimeout(() => {
      console.log('[CodebaseLogger] 定时器触发，开始刷新');
      this.flush();
    }, 5000); // Flush every 5 seconds
  }

  public async flush(): Promise<void> {
    console.log(`[CodebaseLogger] flush被调用 - 索引缓冲区: ${this.indexingBuffer.length}, 检索缓冲区: ${this.retrievalBuffer.length}`);

    if (this.indexingBuffer.length > 0) {
      console.log('[CodebaseLogger] 刷新索引缓冲区');
      await this.writeToFile([...this.indexingBuffer], true);
      this.indexingBuffer = [];
    }

    if (this.retrievalBuffer.length > 0) {
      console.log('[CodebaseLogger] 刷新检索缓冲区');
      await this.writeToFile([...this.retrievalBuffer], false);
      this.retrievalBuffer = [];
    }

    if (this.flushTimeout) {
      console.log('[CodebaseLogger] 清除刷新定时器');
      clearTimeout(this.flushTimeout);
      this.flushTimeout = null;
    }

    console.log('[CodebaseLogger] flush完成');
  }

  private addToBuffer(entry: CodebaseLogEntry, isIndexing: boolean): void {
    console.log(`[CodebaseLogger] addToBuffer被调用 - 条目类型: ${entry.type}, 级别: ${entry.level}, 是否为索引: ${isIndexing}`);
    if (!this.shouldLog(entry.level)) {
      console.log('[CodebaseLogger] 条目被过滤，不添加到缓冲区');
      return;
    }

    const buffer = isIndexing ? this.indexingBuffer : this.retrievalBuffer;
    buffer.push(entry);
    console.log(`[CodebaseLogger] 条目已添加到缓冲区，当前缓冲区大小: ${buffer.length}/${this.config.bufferSize}`);

    if (buffer.length >= this.config.bufferSize) {
      // Flush immediately if buffer is full
      console.log('[CodebaseLogger] 缓冲区已满，立即刷新到文件');
      this.writeToFile([...buffer], isIndexing);
      buffer.length = 0;
    } else {
      console.log('[CodebaseLogger] 缓冲区未满，安排延迟刷新');
      this.scheduleFlush();
    }
  }

  // 索引流程日志记录方法
  public logIndexingStart(data: IndexingStartLogEntry['data'], level: LogLevel = 'info', workspaceDir?: string): void {
    const entry: IndexingStartLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'indexing_start',
      data,
    };
    this.addToBuffer(entry, true);
  }

  public logFileProcessing(data: FileProcessingLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: FileProcessingLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'file_processing',
      data,
    };
    this.addToBuffer(entry, true);
  }

  public logChunkExtraction(data: ChunkExtractionLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: ChunkExtractionLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'chunk_extraction',
      data,
    };
    this.addToBuffer(entry, true);
  }

  public logVectorization(data: VectorizationLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: VectorizationLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'vectorization',
      data,
    };
    this.addToBuffer(entry, true);
  }

  public logIndexingComplete(data: IndexingCompleteLogEntry['data'], level: LogLevel = 'info', workspaceDir?: string): void {
    const entry: IndexingCompleteLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'indexing_complete',
      data,
    };
    this.addToBuffer(entry, true);
  }

  // 检索流程日志记录方法
  public logRetrievalStart(data: RetrievalStartLogEntry['data'], level: LogLevel = 'info', workspaceDir?: string): void {
    const entry: RetrievalStartLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'retrieval_start',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logQueryPreprocessing(data: QueryPreprocessingLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: QueryPreprocessingLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'query_preprocessing',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logVectorSearch(data: VectorSearchLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: VectorSearchLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'vector_search',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logFullTextSearch(data: FullTextSearchLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: FullTextSearchLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'fulltext_search',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logReranking(data: RerankingLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: RerankingLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'reranking',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logContextGeneration(data: ContextGenerationLogEntry['data'], level: LogLevel = 'debug', workspaceDir?: string): void {
    const entry: ContextGenerationLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'context_generation',
      data,
    };
    this.addToBuffer(entry, false);
  }

  public logRetrievalComplete(data: RetrievalCompleteLogEntry['data'], level: LogLevel = 'info', workspaceDir?: string): void {
    const entry: RetrievalCompleteLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      workspaceDir,
      type: 'retrieval_complete',
      data,
    };
    this.addToBuffer(entry, false);
  }

  // 清理旧日志文件
  public async cleanupOldLogs(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.logRetentionDays);

    const logDir = path.dirname(getCodebaseIndexingLogsPath());

    try {
      const files = await fs.promises.readdir(logDir);

      for (const file of files) {
        if (file.endsWith('.jsonl')) {
          const filePath = path.join(logDir, file);
          const stats = await fs.promises.stat(filePath);

          if (stats.mtime < cutoffDate) {
            await fs.promises.unlink(filePath);
          }
        }
      }
    } catch (error) {
      console.error(`Failed to cleanup old codebase logs: ${error}`);
    }
  }

  // 获取当前会话ID
  public getSessionId(): string {
    return this.sessionId;
  }

  // 重新生成会话ID（用于新的索引或检索会话）
  public newSession(): string {
    this.sessionId = this.generateSessionId();
    return this.sessionId;
  }
}
