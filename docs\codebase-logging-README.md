# Codebase日志记录系统

Continue项目的Codebase日志记录系统提供了对代码库索引和检索流程的详细日志记录功能，帮助开发者分析和调试codebase功能的执行过程。

## 功能特性

### 索引流程日志记录
- **文件扫描和处理**: 记录每个文件的处理时间、大小、语言类型
- **代码块提取**: 记录从文件中提取的代码块详情（行号、内容长度、摘要）
- **向量化处理**: 记录向量化操作的时间和结果
- **索引更新**: 记录索引操作的完整统计信息

### 检索流程日志记录
- **查询处理**: 记录原始查询和预处理结果
- **向量检索**: 记录向量搜索的时间、结果数量和相关性分数
- **全文搜索**: 记录BM25搜索的结果和排名
- **重排序算法**: 记录重排序模型的输入输出和性能指标
- **上下文生成**: 记录最终传递给LLM的上下文内容

## 配置方法

在您的 `~/.continue/config.yaml` 文件中添加以下配置：

```yaml
codebaseLogging:
  enabled: true              # 启用日志记录
  logLevel: "debug"          # 日志级别: debug/info/warn/error
  maxLogFileSize: 10485760   # 最大文件大小 (10MB)
  logRetentionDays: 7        # 日志保留天数
  bufferSize: 100            # 缓冲区大小
  format: "human"            # 日志格式: json/human (默认human)
```

### 日志级别说明

- **debug**: 记录所有详细信息，包括每个文件的处理过程、代码块提取、向量化等
- **info**: 记录主要流程节点，如索引开始/完成、检索开始/完成
- **warn**: 仅记录警告和错误信息
- **error**: 仅记录错误信息

### 日志格式说明

- **human**: 人类可读格式，便于直接查看和理解
  ```
  [2024-01-15 10:30:00] INFO  [INDEXING START] 开始索引 - 文件数: 150, 类型: chunks, 分支: main
  [2024-01-15 10:30:01] DEBUG [FILE PROCESSING] 处理文件: /src/main.ts (2048 bytes, 45ms) -> 3 chunks
  ```

- **json**: 结构化JSON格式，便于程序解析和分析
  ```json
  {"timestamp":"2024-01-15T10:30:00.000Z","level":"info","type":"indexing_start","data":{"totalFiles":150}}
  ```

## 日志文件位置

日志文件存储在 `~/.continue/logs/codebase/` 目录下：

- `indexing.jsonl`: 索引流程日志
- `retrieval.jsonl`: 检索流程日志

每个日志条目都是一个JSON对象，包含时间戳、会话ID、日志级别和具体数据。

## 日志格式示例

### 索引开始日志
```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "level": "info",
  "sessionId": "1705312200000-abc123def",
  "workspaceDir": "/path/to/workspace",
  "type": "indexing_start",
  "data": {
    "totalFiles": 150,
    "indexType": "chunks",
    "branch": "main"
  }
}
```

### 文件处理日志
```json
{
  "timestamp": "2024-01-15T10:30:01.000Z",
  "level": "debug",
  "sessionId": "1705312200000-abc123def",
  "workspaceDir": "/path/to/workspace",
  "type": "file_processing",
  "data": {
    "filepath": "/src/components/Button.tsx",
    "fileSize": 2048,
    "language": "typescript",
    "processingTimeMs": 45,
    "chunksGenerated": 3
  }
}
```

### 检索开始日志
```json
{
  "timestamp": "2024-01-15T10:35:00.000Z",
  "level": "info",
  "sessionId": "1705312500000-xyz789ghi",
  "workspaceDir": "/path/to/workspace",
  "type": "retrieval_start",
  "data": {
    "originalQuery": "how to implement authentication",
    "fullInput": "how to implement authentication in React",
    "nRetrieve": 25,
    "nFinal": 10,
    "useReranking": true,
    "filterDirectory": "/src"
  }
}
```

### 向量搜索日志
```json
{
  "timestamp": "2024-01-15T10:35:01.000Z",
  "level": "debug",
  "sessionId": "1705312500000-xyz789ghi",
  "workspaceDir": "/path/to/workspace",
  "type": "vector_search",
  "data": {
    "query": "how to implement authentication in React",
    "embeddingModel": "text-embedding-ada-002",
    "searchTimeMs": 120,
    "resultsCount": 15,
    "results": [
      {
        "filepath": "/src/auth/AuthProvider.tsx",
        "chunkIndex": 0,
        "score": 0.95,
        "startLine": 1,
        "endLine": 25,
        "contentPreview": "import React, { createContext, useContext } from 'react';\n\ninterface AuthContextType {"
      }
    ]
  }
}
```

### 重排序日志
```json
{
  "timestamp": "2024-01-15T10:35:02.000Z",
  "level": "debug",
  "sessionId": "1705312500000-xyz789ghi",
  "workspaceDir": "/path/to/workspace",
  "type": "reranking",
  "data": {
    "query": "how to implement authentication in React",
    "rerankModel": "rerank-english-v2.0",
    "inputChunksCount": 15,
    "outputChunksCount": 10,
    "processingTimeMs": 200,
    "scores": [0.92, 0.88, 0.85, 0.82, 0.78, 0.75, 0.72, 0.68, 0.65, 0.62],
    "rerankedResults": [
      {
        "filepath": "/src/auth/AuthProvider.tsx",
        "chunkIndex": 0,
        "originalScore": 0.95,
        "rerankScore": 0.92,
        "startLine": 1,
        "endLine": 25
      }
    ]
  }
}
```

## 日志分析

您可以使用各种工具来分析日志文件：

### 使用jq查询日志
```bash
# 查看所有索引操作
cat ~/.continue/logs/codebase/indexing.jsonl | jq 'select(.type == "indexing_start")'

# 查看处理时间超过100ms的文件
cat ~/.continue/logs/codebase/indexing.jsonl | jq 'select(.type == "file_processing" and .data.processingTimeMs > 100)'

# 查看检索结果数量
cat ~/.continue/logs/codebase/retrieval.jsonl | jq 'select(.type == "retrieval_complete") | .data.finalResultsCount'
```

### 使用Python分析
```python
import json
import pandas as pd

# 读取索引日志
with open('~/.continue/logs/codebase/indexing.jsonl', 'r') as f:
    indexing_logs = [json.loads(line) for line in f]

# 分析文件处理时间
file_processing_logs = [log for log in indexing_logs if log['type'] == 'file_processing']
df = pd.DataFrame([log['data'] for log in file_processing_logs])
print(f"平均处理时间: {df['processingTimeMs'].mean():.2f}ms")
print(f"最慢的文件: {df.loc[df['processingTimeMs'].idxmax(), 'filepath']}")
```

## 性能影响

- 日志记录采用异步写入和缓冲机制，对正常功能的性能影响最小
- 建议在生产环境中使用 `info` 或 `warn` 级别以减少日志量
- 日志文件会自动轮转和清理，避免占用过多磁盘空间

## 故障排除

如果遇到问题，请检查：

1. 配置文件语法是否正确
2. 日志目录是否有写入权限
3. 磁盘空间是否充足
4. 日志级别设置是否合适

## 注意事项

- 日志文件可能包含代码片段，请注意保护敏感信息
- 在debug级别下，日志文件可能会快速增长
- 建议定期检查和清理旧的日志文件
